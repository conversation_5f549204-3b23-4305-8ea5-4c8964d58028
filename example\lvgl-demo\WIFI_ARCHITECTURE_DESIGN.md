# ESP32-S3 LVGL WiFi配置功能架构设计

## 🎯 功能概述

在现有三屏导航系统基础上，添加WiFi配置管理功能，包括密码验证、WiFi扫描、连接和状态显示。

## 📋 屏幕扩展设计

### 新增屏幕定义
```c
typedef enum {
    SCREEN_INITIAL = 0,     // Screen 1: 初始屏幕（增强）
    SCREEN_MENU,            // Screen 2: 菜单屏幕
    SCREEN_QRCODE,          // Screen 3: QR码屏幕
    SCREEN_PASSWORD,        // Screen 4: 密码验证屏幕
    SCREEN_WIFI_CONFIG,     // Screen 5: WiFi配置屏幕
    SCREEN_WIFI_PASSWORD,   // Screen 6: WiFi密码输入屏幕
    SCREEN_COUNT            // 屏幕总数
} screen_type_t;
```

### 屏幕导航流程
```
Screen 1 (初始屏幕)
    ├─ 长按标题 → Screen 4 (密码验证)
    ├─ 左/右按钮 → Screen 2 (菜单屏幕)
    └─ WiFi状态指示器 (只读显示)

Screen 4 (密码验证)
    ├─ 密码正确 → Screen 5 (WiFi配置)
    ├─ 密码错误 → 错误提示 → Screen 1
    └─ 取消 → Screen 1

Screen 5 (WiFi配置)
    ├─ 选择WiFi → Screen 6 (WiFi密码输入)
    ├─ 刷新 → 重新扫描
    └─ 返回 → Screen 1

Screen 6 (WiFi密码输入)
    ├─ 连接成功 → 成功提示 → Screen 1
    ├─ 连接失败 → 错误提示 → 重新输入
    └─ 取消 → Screen 5
```

## 🔧 数据结构设计

### WiFi状态管理
```c
typedef enum {
    WIFI_STATUS_DISCONNECTED = 0,
    WIFI_STATUS_CONNECTING,
    WIFI_STATUS_CONNECTED,
    WIFI_STATUS_FAILED,
    WIFI_STATUS_SCANNING
} wifi_status_t;

typedef struct {
    char ssid[33];          // WiFi名称
    int8_t rssi;            // 信号强度
    uint8_t authmode;       // 加密类型
    bool is_open;           // 是否开放网络
} wifi_ap_info_t;

typedef struct {
    wifi_status_t status;
    char connected_ssid[33];
    int8_t signal_strength;
    wifi_ap_info_t* ap_list;
    uint16_t ap_count;
    bool auto_connect_enabled;
} wifi_manager_t;
```

### 密码验证管理
```c
typedef struct {
    uint8_t failed_attempts;
    uint32_t lockout_start_time;
    bool is_locked;
    char input_buffer[5];   // 4位密码 + 终止符
    uint8_t input_length;
} password_manager_t;
```

### 扩展的屏幕管理器
```c
typedef struct {
    screen_type_t current_screen;
    screen_type_t previous_screen;
    lv_obj_t* screen_objects[SCREEN_COUNT];
    lv_obj_t* qr_canvas;
    wifi_manager_t wifi_manager;
    password_manager_t password_manager;
    bool is_initialized;
} screen_manager_t;
```

## 🎨 界面设计规范

### Screen 1 增强功能
- **WiFi状态指示器位置**: 右上角 (200, 10)
- **指示器尺寸**: 30x20像素
- **长按检测**: 标题标签，2.5秒触发
- **状态图标**:
  - 已连接: "📶" + 信号强度条
  - 未连接: "📵"
  - 连接中: "🔄" (旋转动画)

### Screen 4 密码验证界面
- **布局**: 3x4数字键盘 + 输入框 + 控制按钮
- **数字键盘**: 每个按键 60x40像素，间距10像素
- **输入框**: 显示"****"遮挡字符
- **按钮**: 确认(绿色) 取消(红色)

### Screen 5 WiFi配置界面
- **标题**: "WiFi设置" (顶部居中)
- **扫描进度**: 进度条 + "扫描中..."文字
- **WiFi列表**: 滚动列表，每项包含SSID + 信号图标 + 加密图标
- **控制按钮**: 刷新、返回 (底部)

### Screen 6 WiFi密码输入界面
- **选中WiFi显示**: 顶部显示SSID
- **密码输入框**: 支持字母数字特殊字符
- **虚拟键盘**: 全键盘布局
- **连接进度**: 进度指示器
- **控制按钮**: 连接、取消

## 🔐 安全机制设计

### 密码验证策略
```c
#define ADMIN_PASSWORD "2658"
#define MAX_PASSWORD_ATTEMPTS 3
#define LOCKOUT_DURATION_MS (5 * 60 * 1000)  // 5分钟

bool validate_admin_password(const char* input);
bool is_password_locked(void);
void reset_password_attempts(void);
void increment_failed_attempts(void);
```

### WiFi密码存储
```c
// 使用NVS (Non-Volatile Storage) 存储WiFi凭据
#define NVS_WIFI_NAMESPACE "wifi_config"
#define NVS_WIFI_SSID_KEY "ssid"
#define NVS_WIFI_PASSWORD_KEY "password"

esp_err_t save_wifi_credentials(const char* ssid, const char* password);
esp_err_t load_wifi_credentials(char* ssid, char* password);
esp_err_t clear_wifi_credentials(void);
```

## 📡 WiFi API集成

### ESP32 WiFi功能封装
```c
// WiFi初始化和事件处理
esp_err_t wifi_manager_init(void);
esp_err_t wifi_start_scan(void);
esp_err_t wifi_connect(const char* ssid, const char* password);
esp_err_t wifi_disconnect(void);
wifi_status_t wifi_get_status(void);
int8_t wifi_get_signal_strength(void);

// 事件回调
void wifi_event_handler(void* arg, esp_event_base_t event_base, 
                       int32_t event_id, void* event_data);
```

### 扫描结果处理
```c
typedef struct {
    wifi_ap_info_t* ap_list;
    uint16_t ap_count;
    bool scan_complete;
} wifi_scan_result_t;

esp_err_t wifi_get_scan_results(wifi_scan_result_t* results);
void wifi_free_scan_results(wifi_scan_result_t* results);
```

## 🎯 实现优先级

### Phase 1: 基础架构 (高优先级)
1. 扩展头文件定义
2. 实现Screen 1增强功能
3. 实现Screen 4密码验证

### Phase 2: WiFi核心功能 (高优先级)
1. 集成WiFi API功能
2. 实现Screen 5 WiFi配置
3. 实现Screen 6密码输入

### Phase 3: 优化和测试 (中优先级)
1. 内存优化
2. 用户体验优化
3. 错误处理完善
4. 全面测试

## 🧠 内存管理策略

### 内存分配原则
- WiFi扫描结果使用动态分配，及时释放
- 屏幕对象按需创建，缓存常用屏幕
- 字符串缓冲区使用栈分配，避免碎片

### 内存监控
```c
void print_memory_usage(const char* location);
bool check_memory_available(size_t required_bytes);
```

## 🔄 状态机设计

### 主状态机
```c
typedef enum {
    APP_STATE_NORMAL,       // 正常三屏导航
    APP_STATE_WIFI_CONFIG,  // WiFi配置模式
    APP_STATE_PASSWORD_AUTH // 密码验证模式
} app_state_t;
```

### WiFi连接状态机
```c
typedef enum {
    WIFI_STATE_IDLE,
    WIFI_STATE_SCANNING,
    WIFI_STATE_CONNECTING,
    WIFI_STATE_CONNECTED,
    WIFI_STATE_FAILED
} wifi_state_t;
```

## 📊 性能指标

### 响应时间要求
- 屏幕切换: < 200ms
- WiFi扫描: < 10s
- WiFi连接: < 30s
- 密码验证: < 100ms

### 内存使用限制
- WiFi扫描结果: < 8KB
- 屏幕对象缓存: < 20KB
- 总增量内存: < 32KB

## 🧪 测试策略

### 功能测试
1. 所有屏幕导航路径
2. WiFi扫描和连接流程
3. 密码验证和安全机制
4. 错误处理和恢复

### 性能测试
1. 内存使用监控
2. 响应时间测量
3. 长时间运行稳定性
4. 并发操作处理

### 兼容性测试
1. 不同WiFi网络类型
2. 各种信号强度环境
3. 网络异常情况处理
4. 中文字体显示兼容性

---

**设计版本**: v1.0  
**设计日期**: 2025-08-02  
**状态**: 架构设计完成，准备实现  
**下一步**: 开始扩展头文件定义
