#include <stdio.h>
#include "string.h"
#include "esp_log.h"
#include "esp_heap_caps.h"
#include "esp_system.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "qmsd_board.h"
#include "qmsd_utils.h"
#include "lv_demo_widgets.h"
#include "qrcode_lvgl.h"
#include "screen_manager.h"

#define TAG "QMSD-MAIN"

#ifdef CONFIG_QMSD_GUI_LVGL_V7
#error "example not support lvgl 7 now"
#endif

// 注意：现在使用3屏导航系统，通过screen_manager.h管理

// 内部函数前向声明
static void navigation_system_init_timer_cb(lv_timer_t* timer);

/**
 * @brief 导航系统初始化定时器回调函数
 * @param timer 定时器对象
 */
static void navigation_system_init_timer_cb(lv_timer_t* timer)
{
    ESP_LOGI(TAG, "Initializing 3-screen navigation system...");

    // 检查可用内存
    size_t free_heap = esp_get_free_heap_size();
    ESP_LOGI(TAG, "Free heap before navigation init: %zu bytes", free_heap);

    if (free_heap < 50000) {  // 至少需要50KB内存
        ESP_LOGE(TAG, "Insufficient memory for navigation system: %zu bytes", free_heap);
        lv_timer_del(timer);
        return;
    }

    // 初始化屏幕管理器
    if (screen_manager_init()) {
        ESP_LOGI(TAG, "3-screen navigation system initialized successfully");
        screen_manager_print_status();
    } else {
        ESP_LOGE(TAG, "Failed to initialize navigation system");
    }

    // 删除一次性定时器
    lv_timer_del(timer);
}

void gui_user_init()
{
    ESP_LOGI(TAG, "Starting 3-screen navigation system...");

    // 延迟初始化导航系统，给LVGL时间完成初始化
    lv_timer_create(navigation_system_init_timer_cb, 2000, NULL);  // 2秒延迟

    ESP_LOGI(TAG, "GUI user initialization complete - navigation system will start shortly");
}

void app_main(void)
{   
    gpio_install_isr_service(ESP_INTR_FLAG_SHARED);
    qmsd_board_config_t config = QMSD_BOARD_DEFAULT_CONFIG;
    config.gui.refresh_task.core = 1;
    qmsd_board_init(&config);
    printf("Fine qmsd!\r\n");
}
