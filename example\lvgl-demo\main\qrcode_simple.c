/**
 * @file qrcode_simple.c
 * @brief 最简化的QR码显示实现，确保编译通过
 * <AUTHOR> Agent
 * @date 2025-08-01
 */

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include "esp_log.h"
#include "esp_heap_caps.h"
#include "esp_system.h"
#include "qrcode_lvgl.h"

static const char *TAG = "QRCODE_SIMPLE";

/**
 * @brief 测试QR码生成功能（仅控制台输出）
 */
void qrcode_test_generation(void)
{
    ESP_LOGI(TAG, "Testing QR code generation...");
    
    const char* test_text = "Hello World";
    
    // 分配缓冲区
    uint8_t *qrcode = malloc(qrcodegen_BUFFER_LEN_MAX);
    uint8_t *temp_buffer = malloc(qrcodegen_BUFFER_LEN_MAX);
    
    if (!qrcode || !temp_buffer) {
        ESP_LOGE(TAG, "Failed to allocate test buffers");
        if (qrcode) free(qrcode);
        if (temp_buffer) free(temp_buffer);
        return;
    }
    
    memset(qrcode, 0, qrcodegen_BUFFER_LEN_MAX);
    memset(temp_buffer, 0, qrcodegen_BUFFER_LEN_MAX);
    
    // 生成测试二维码
    bool success = qrcodegen_encodeText(
        test_text,
        temp_buffer,
        qrcode,
        qrcodegen_Ecc_LOW,
        qrcodegen_VERSION_MIN,
        qrcodegen_VERSION_MAX,
        qrcodegen_Mask_AUTO,
        true
    );
    
    if (success) {
        int size = qrcodegen_getSize(qrcode);
        ESP_LOGI(TAG, "Test QR code generated successfully, size: %d x %d", size, size);
        
        // 简单的控制台输出（用于调试）
        printf("\nQR Code for '%s' (size: %d):\n", test_text, size);
        for (int y = -1; y <= size; y++) {
            for (int x = -1; x <= size; x++) {
                if (qrcodegen_getModule(qrcode, x, y)) {
                    printf("##");
                } else {
                    printf("  ");
                }
            }
            printf("\n");
        }
        printf("\n");
    } else {
        ESP_LOGE(TAG, "Failed to generate test QR code");
    }
    
    free(qrcode);
    free(temp_buffer);
}

/**
 * @brief 简化版本的QR码显示函数
 * @param parent 父对象
 * @param text 要编码的文本
 * @param x_pos X坐标
 * @param y_pos Y坐标
 * @param scale 缩放比例
 * @return 容器对象指针或NULL
 */
lv_obj_t* qrcode_create_simple(lv_obj_t* parent, const char* text, lv_coord_t x_pos, lv_coord_t y_pos, int scale)
{
    // 严格的参数验证
    if (!parent) {
        ESP_LOGE(TAG, "Parent object is NULL");
        return NULL;
    }

    if (!text || strlen(text) == 0) {
        ESP_LOGE(TAG, "Text is NULL or empty");
        return NULL;
    }

    // 检查可用内存
    size_t free_heap = esp_get_free_heap_size();
    ESP_LOGI(TAG, "Free heap before QR creation: %zu bytes", free_heap);

    if (free_heap < 30000) {  // 至少需要30KB
        ESP_LOGE(TAG, "Insufficient memory for QR code creation: %zu bytes", free_heap);
        return NULL;
    }

    ESP_LOGI(TAG, "Creating simple QR code for: %.30s%s", text, strlen(text) > 30 ? "..." : "");

    // 分配QR码生成所需的缓冲区
    uint8_t *qrcode = malloc(qrcodegen_BUFFER_LEN_MAX);
    uint8_t *temp_buffer = malloc(qrcodegen_BUFFER_LEN_MAX);

    if (!qrcode || !temp_buffer) {
        ESP_LOGE(TAG, "Failed to allocate memory for QR code generation (need %d bytes each)",
                 qrcodegen_BUFFER_LEN_MAX);
        if (qrcode) free(qrcode);
        if (temp_buffer) free(temp_buffer);
        return NULL;
    }
    
    // 清零缓冲区
    memset(qrcode, 0, qrcodegen_BUFFER_LEN_MAX);
    memset(temp_buffer, 0, qrcodegen_BUFFER_LEN_MAX);
    
    // 生成二维码
    bool success = qrcodegen_encodeText(
        text,
        temp_buffer,
        qrcode,
        qrcodegen_Ecc_LOW,           // 低错误纠正级别
        qrcodegen_VERSION_MIN,       // 最小版本
        qrcodegen_VERSION_MAX,       // 最大版本
        qrcodegen_Mask_AUTO,         // 自动选择掩码
        true                         // 提升错误纠正级别
    );
    
    if (!success) {
        ESP_LOGE(TAG, "Failed to generate QR code");
        free(qrcode);
        free(temp_buffer);
        return NULL;
    }
    
    // 获取二维码尺寸
    int qr_size = qrcodegen_getSize(qrcode);
    ESP_LOGI(TAG, "Generated QR code size: %d x %d", qr_size, qr_size);
    
    // 如果没有指定缩放比例，自动计算
    if (scale <= 0) {
        scale = 180 / qr_size;  // 目标尺寸约180像素
        if (scale < 2) scale = 2;
        if (scale > 5) scale = 5;
    }
    
    // 计算总尺寸
    int border = 2;  // 边框大小
    int total_size = (qr_size + border * 2) * scale;
    
    ESP_LOGI(TAG, "QR code scale: %d, total size: %d x %d", scale, total_size, total_size);
    
    // 验证父对象的有效性
    if (!lv_obj_is_valid(parent)) {
        ESP_LOGE(TAG, "Parent object is not valid");
        free(qrcode);
        free(temp_buffer);
        return NULL;
    }

    // 创建容器 - 添加更多安全检查
    ESP_LOGI(TAG, "Creating container object...");
    lv_obj_t *container = lv_obj_create(parent);
    if (!container) {
        ESP_LOGE(TAG, "Failed to create container object");
        free(qrcode);
        free(temp_buffer);
        return NULL;
    }

    // 验证容器创建成功
    if (!lv_obj_is_valid(container)) {
        ESP_LOGE(TAG, "Created container is not valid");
        free(qrcode);
        free(temp_buffer);
        return NULL;
    }

    ESP_LOGI(TAG, "Container created successfully, setting properties...");

    // 安全地设置容器属性
    lv_obj_set_size(container, total_size, total_size);
    lv_obj_set_pos(container, x_pos, y_pos);
    lv_obj_set_style_bg_color(container, lv_color_white(), 0);
    lv_obj_set_style_border_width(container, 0, 0);
    lv_obj_set_style_pad_all(container, 0, 0);
    lv_obj_clear_flag(container, LV_OBJ_FLAG_SCROLLABLE);
    
    // 绘制二维码模块 - 添加安全限制
    int modules_created = 0;
    int max_modules = 2000;  // 限制最大模块数量，防止内存耗尽

    ESP_LOGI(TAG, "Starting to create QR modules (max: %d)...", max_modules);

    for (int y = 0; y < qr_size && modules_created < max_modules; y++) {
        for (int x = 0; x < qr_size && modules_created < max_modules; x++) {
            if (qrcodegen_getModule(qrcode, x, y)) {
                // 检查内存状况
                if (modules_created % 100 == 0) {  // 每100个模块检查一次
                    size_t free_heap = esp_get_free_heap_size();
                    if (free_heap < 10000) {  // 如果剩余内存少于10KB，停止创建
                        ESP_LOGW(TAG, "Low memory (%zu bytes), stopping module creation at %d modules",
                                free_heap, modules_created);
                        break;
                    }
                }

                // 创建黑色矩形表示暗模块
                lv_obj_t *module = lv_obj_create(container);
                if (module && lv_obj_is_valid(module)) {
                    lv_obj_set_size(module, scale, scale);
                    lv_obj_set_pos(module, (x + border) * scale, (y + border) * scale);
                    lv_obj_set_style_bg_color(module, lv_color_black(), 0);
                    lv_obj_set_style_border_width(module, 0, 0);
                    lv_obj_set_style_radius(module, 0, 0);
                    lv_obj_clear_flag(module, LV_OBJ_FLAG_CLICKABLE);
                    lv_obj_clear_flag(module, LV_OBJ_FLAG_SCROLLABLE);
                    modules_created++;
                } else {
                    ESP_LOGW(TAG, "Failed to create module at (%d, %d), created %d modules so far",
                            x, y, modules_created);
                    // 如果创建失败，可能是内存不足，停止创建更多模块
                    if (modules_created > 50) {  // 如果已经创建了一些模块，继续使用
                        goto module_creation_done;
                    } else {
                        // 如果连基本模块都创建不了，清理并返回错误
                        ESP_LOGE(TAG, "Critical: Cannot create basic QR modules");
                        lv_obj_del(container);
                        free(qrcode);
                        free(temp_buffer);
                        return NULL;
                    }
                }
            }
        }
    }

module_creation_done:
    
    // 释放临时缓冲区
    free(qrcode);
    free(temp_buffer);
    
    ESP_LOGI(TAG, "Simple QR code created successfully at (%d, %d), modules: %d", 
             x_pos, y_pos, modules_created);
    
    return container;
}

/**
 * @brief 生成并显示支付宝二维码（简化版本）
 * @param parent 父对象
 * @return 容器对象指针或NULL
 */
lv_obj_t* qrcode_show_alipay_simple(lv_obj_t* parent)
{
    if (!parent) {
        ESP_LOGE(TAG, "Parent object is NULL");
        return NULL;
    }
    
    // 计算居中位置
    lv_coord_t x_pos = QRCODE_CENTER_X(180);  // QR码约180像素
    lv_coord_t y_pos = QRCODE_CENTER_Y(180);

    ESP_LOGI(TAG, "Creating simple Alipay QR code at center position (%d, %d)", x_pos, y_pos);

    return qrcode_create_simple(parent, QRCODE_ALIPAY_URL, x_pos, y_pos, 0);
}
