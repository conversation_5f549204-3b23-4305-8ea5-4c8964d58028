/**
 * @file heap_test.c
 * @brief 堆内存函数测试验证
 * <AUTHOR> Agent
 * @date 2025-08-01
 * 
 * 用于验证esp_get_free_heap_size()函数是否正确工作
 */

#include <stdio.h>
#include "esp_log.h"
#include "esp_system.h"
#include "esp_heap_caps.h"

static const char *TAG = "HEAP_TEST";

/**
 * @brief 测试堆内存函数
 */
void test_heap_functions(void)
{
    ESP_LOGI(TAG, "=== Heap Memory Function Test ===");
    
    // 测试基本的堆内存查询函数
    size_t free_heap = esp_get_free_heap_size();
    size_t min_free_heap = esp_get_minimum_free_heap_size();
    
    ESP_LOGI(TAG, "Current free heap: %zu bytes", free_heap);
    ESP_LOGI(TAG, "Minimum free heap: %zu bytes", min_free_heap);
    
    // 测试不同类型的内存
    size_t free_internal = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
    size_t free_spiram = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
    
    ESP_LOGI(TAG, "Free internal RAM: %zu bytes", free_internal);
    ESP_LOGI(TAG, "Free SPIRAM: %zu bytes", free_spiram);
    
    // 测试内存分配和释放
    ESP_LOGI(TAG, "Testing memory allocation...");
    
    size_t test_size = 1024;  // 1KB测试
    void* test_ptr = malloc(test_size);
    
    if (test_ptr) {
        size_t free_after_alloc = esp_get_free_heap_size();
        ESP_LOGI(TAG, "Allocated %zu bytes, free heap now: %zu bytes", 
                 test_size, free_after_alloc);
        ESP_LOGI(TAG, "Memory used: %zu bytes", free_heap - free_after_alloc);
        
        // 释放内存
        free(test_ptr);
        size_t free_after_free = esp_get_free_heap_size();
        ESP_LOGI(TAG, "Memory freed, free heap now: %zu bytes", free_after_free);
    } else {
        ESP_LOGE(TAG, "Failed to allocate test memory");
    }
    
    ESP_LOGI(TAG, "=== Heap Memory Function Test Complete ===");
}

/**
 * @brief 检查内存是否充足
 * @param required_bytes 需要的字节数
 * @return true if sufficient memory available
 */
bool check_memory_sufficient(size_t required_bytes)
{
    size_t free_heap = esp_get_free_heap_size();
    size_t safety_margin = 10000;  // 10KB安全余量
    
    ESP_LOGI(TAG, "Memory check: required=%zu, available=%zu, margin=%zu", 
             required_bytes, free_heap, safety_margin);
    
    if (free_heap < required_bytes + safety_margin) {
        ESP_LOGW(TAG, "Insufficient memory: need %zu + %zu = %zu, have %zu", 
                 required_bytes, safety_margin, required_bytes + safety_margin, free_heap);
        return false;
    }
    
    ESP_LOGI(TAG, "Memory check passed");
    return true;
}
