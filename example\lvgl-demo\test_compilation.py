#!/usr/bin/env python3
"""
ESP32 LVGL WiFi项目编译测试脚本
用于验证编译错误修复的效果
"""

import subprocess
import sys
import os
import time

def run_command(cmd, cwd=None, timeout=120):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            cwd=cwd, 
            capture_output=True, 
            text=True, 
            timeout=timeout
        )
        return result.returncode, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return -1, "", "Command timed out"
    except Exception as e:
        return -1, "", str(e)

def test_compilation():
    """测试编译"""
    print("🔧 ESP32 LVGL WiFi项目编译测试")
    print("=" * 50)
    
    # 项目路径
    project_path = r"s:\debug_stm32_wawaji_128\office_esp32s3\qmsd-esp32-bsp\example\lvgl-demo"
    
    if not os.path.exists(project_path):
        print(f"❌ 项目路径不存在: {project_path}")
        return False
    
    print(f"📁 项目路径: {project_path}")
    
    # 设置ESP-IDF环境
    idf_path = r"d:\program_me_dev\Espressif\frameworks\v5.4\esp-idf"
    python_path = r"D:\program_me_dev\Espressif\python_env\idf5.4_py3.11_env\Scripts\python.exe"
    idf_py = os.path.join(idf_path, "tools", "idf.py")
    
    # 构建编译命令
    cmd = f'"{python_path}" "{idf_py}" build'
    
    print(f"🔨 编译命令: {cmd}")
    print("⏳ 开始编译...")
    
    start_time = time.time()
    returncode, stdout, stderr = run_command(cmd, cwd=project_path, timeout=300)
    end_time = time.time()
    
    print(f"⏱️  编译耗时: {end_time - start_time:.1f}秒")
    
    if returncode == 0:
        print("✅ 编译成功！")
        print("\n📊 编译统计:")
        if "Linking" in stdout:
            print("  - 链接阶段完成")
        if "Generating" in stdout:
            print("  - 固件生成完成")
        return True
    else:
        print("❌ 编译失败")
        print(f"返回码: {returncode}")
        
        # 分析错误
        if stderr:
            print("\n🚨 错误信息:")
            print(stderr[:1000])  # 只显示前1000字符
            
            # 检查特定错误
            if "lv_timer_get_user_data" in stderr:
                print("\n🔍 检测到LVGL API错误 - 需要修复timer->user_data访问")
            if "unused variable" in stderr:
                print("\n🔍 检测到未使用变量警告")
            if "implicit declaration" in stderr:
                print("\n🔍 检测到隐式函数声明错误")
        
        if stdout:
            print("\n📝 输出信息:")
            print(stdout[:500])  # 只显示前500字符
            
        return False

def check_fixes():
    """检查修复是否正确应用"""
    print("\n🔍 检查修复状态")
    print("-" * 30)
    
    screen_manager_path = r"s:\debug_stm32_wawaji_128\office_esp32s3\qmsd-esp32-bsp\example\lvgl-demo\main\screen_manager.c"
    
    if not os.path.exists(screen_manager_path):
        print("❌ screen_manager.c文件不存在")
        return False
    
    try:
        with open(screen_manager_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查修复1: timer->user_data
        if "timer->user_data" in content:
            print("✅ 修复1: LVGL v8 timer API已修复")
        else:
            print("❌ 修复1: 未找到timer->user_data")
        
        # 检查修复2和3: roller变量使用
        if "lv_roller_get_selected(roller)" in content:
            print("✅ 修复2&3: roller变量使用已修复")
        else:
            print("❌ 修复2&3: roller变量使用未修复")
        
        # 检查是否还有旧的API调用
        if "lv_timer_get_user_data" in content:
            print("❌ 警告: 仍然存在旧的lv_timer_get_user_data调用")
        else:
            print("✅ 确认: 已移除所有lv_timer_get_user_data调用")
        
        return True
        
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 ESP32 LVGL WiFi项目编译错误修复验证")
    print("=" * 60)
    
    # 检查修复状态
    if not check_fixes():
        print("\n❌ 修复检查失败，请检查代码修复")
        return 1
    
    # 测试编译
    if test_compilation():
        print("\n🎉 所有测试通过！编译错误修复成功")
        return 0
    else:
        print("\n❌ 编译测试失败，需要进一步调试")
        return 1

if __name__ == "__main__":
    sys.exit(main())
