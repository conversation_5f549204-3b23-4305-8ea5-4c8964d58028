# ESP32-S3 LVGL 3屏导航系统

## 项目概述

基于ESP32-S3和LVGL 8.3.1的3屏导航系统，实现了从初始屏幕到菜单屏幕再到QR码显示屏幕的完整用户界面流程。

## 屏幕架构

### 🏠 Screen 1: 初始屏幕 (Initial Screen)
- **功能**: 系统启动时显示的欢迎屏幕
- **布局**: 
  - 标题: "Welcome Screen"
  - 两个水平居中的按钮: "Left Button" 和 "Right Button"
- **交互**: 点击任一按钮跳转到Screen 2
- **背景色**: 深蓝色 (#003366)

### 📋 Screen 2: 菜单屏幕 (Menu Screen)  
- **功能**: 主菜单界面，提供4个选项
- **布局**:
  - 标题: "Menu Screen"
  - 2x2网格布局的4个按钮: "Button 1", "Button 2", "Button 3", "Button 4"
- **交互**: 点击任一按钮跳转到Screen 3
- **背景色**: 深绿色 (#2E7D32)

### 📱 Screen 3: QR码屏幕 (QR Code Screen)
- **功能**: 显示支付宝QR码
- **布局**:
  - 标题: "QR Code Screen"
  - 居中显示的QR码（使用内存安全的画布实现）
  - 底部提示: "Touch QR code to return"
- **交互**: 点击QR码返回到Screen 1
- **背景色**: 深蓝紫色 (#1A237E)
- **QR码内容**: 使用全局变量 `QRCODE_ALIPAY_URL`

## 技术实现

### 核心组件

#### 1. 屏幕管理器 (`screen_manager.h/c`)
```c
typedef enum {
    SCREEN_INITIAL = 0,    // Screen 1: 初始屏幕
    SCREEN_MENU,           // Screen 2: 菜单屏幕  
    SCREEN_QRCODE,         // Screen 3: QR码屏幕
    SCREEN_COUNT
} screen_type_t;
```

**主要功能**:
- `screen_manager_init()` - 初始化屏幕管理器
- `screen_manager_switch_to()` - 切换到指定屏幕
- `screen_manager_go_back()` - 返回上一屏幕
- `screen_manager_cleanup()` - 清理资源

#### 2. 内存安全的QR码实现 (`qrcode_safe.c`)
- 使用LVGL画布而不是大量小对象
- 优先使用PSRAM存储画布缓冲区
- 实现完整的内存检查和错误处理

#### 3. 事件处理系统
每个屏幕都有专门的事件处理函数：
- `screen_initial_button_handler()` - 初始屏幕按钮事件
- `screen_menu_button_handler()` - 菜单屏幕按钮事件  
- `screen_qrcode_touch_handler()` - QR码触摸事件

### 导航流程

```
启动 → Screen 1 (Initial) → Screen 2 (Menu) → Screen 3 (QR Code)
  ↑                                                      ↓
  ←←←←←←←←←←←←← 点击QR码返回 ←←←←←←←←←←←←←←←←←←←←←←←←←←
```

### 内存管理策略

#### 1. 延迟初始化
```c
// 2秒延迟确保LVGL完全初始化
lv_timer_create(navigation_system_init_timer_cb, 2000, NULL);
```

#### 2. 按需创建屏幕
- 屏幕对象只在首次访问时创建
- 避免一次性创建所有屏幕造成内存压力

#### 3. 安全的资源清理
```c
// QR码屏幕切换时自动清理画布资源
if (g_screen_manager.current_screen == SCREEN_QRCODE && g_screen_manager.qr_canvas) {
    qrcode_canvas_safe_cleanup(g_screen_manager.qr_canvas);
    g_screen_manager.qr_canvas = NULL;
}
```

#### 4. 内存监控
```c
size_t free_heap = esp_get_free_heap_size();
if (free_heap < 50000) {  // 至少需要50KB
    ESP_LOGE(TAG, "Insufficient memory");
    return false;
}
```

## 文件结构

```
main/
├── screen_manager.h          # 屏幕管理器头文件
├── screen_manager.c          # 屏幕管理器实现
├── qrcode_safe.c            # 内存安全的QR码实现
├── qrcode_lvgl.h            # QR码LVGL接口
├── qrcode_simple.c          # 简化版QR码实现
├── qrcodegen.h/c            # QR码生成库
├── main.c                   # 主程序入口
└── heap_test.c              # 内存测试工具
```

## 使用方法

### 1. 编译和烧录
```bash
cd example/lvgl-demo
idf.py build
idf.py flash monitor
```

### 2. 运行时行为
1. **系统启动**: 显示初始屏幕，包含Left/Right按钮
2. **进入菜单**: 点击任一按钮进入菜单屏幕
3. **显示QR码**: 点击任一菜单按钮显示QR码
4. **返回首页**: 点击QR码返回初始屏幕

### 3. 调试功能
```c
// 打印屏幕管理器状态
screen_manager_print_status();

// 测试内存函数
test_heap_functions();

// 检查内存充足性
bool sufficient = check_memory_sufficient(required_bytes);
```

## 性能特性

### 内存使用优化
- **QR码显示**: 51KB连续内存 vs 原来的137KB+碎片
- **屏幕切换**: 平滑过渡，无内存泄漏
- **资源管理**: 自动清理不需要的对象

### 响应性能
- **启动时间**: 2秒延迟确保稳定初始化
- **切换速度**: 即时屏幕切换
- **触摸响应**: 快速事件处理

### 稳定性保证
- **内存检查**: 每次操作前验证可用内存
- **对象验证**: 确保LVGL对象有效性
- **错误恢复**: 优雅处理失败情况

## 扩展指南

### 添加新屏幕
1. 在`screen_type_t`枚举中添加新类型
2. 实现`screen_create_xxx()`函数
3. 添加相应的事件处理器
4. 更新屏幕切换逻辑

### 自定义按钮样式
```c
lv_obj_t* btn = create_centered_button(parent, "Custom Button", 
                                      x_offset, y_offset, width, height,
                                      custom_handler, user_data);
// 自定义样式
lv_obj_set_style_bg_color(btn, lv_color_hex(0xFF5722), 0);
lv_obj_set_style_radius(btn, 15, 0);
```

### 添加动画效果
```c
// 屏幕切换动画
lv_scr_load_anim(target_screen, LV_SCR_LOAD_ANIM_SLIDE_LEFT, 300, 0, false);
```

## 故障排除

### 常见问题

1. **编译错误**: 确保所有头文件正确包含
2. **内存不足**: 检查PSRAM配置和可用内存
3. **屏幕不显示**: 验证LVGL初始化时序
4. **触摸无响应**: 确认事件处理器正确注册

### 调试技巧

1. **启用详细日志**:
   ```c
   esp_log_level_set("SCREEN_MGR", ESP_LOG_DEBUG);
   ```

2. **监控内存使用**:
   ```c
   ESP_LOGI(TAG, "Free heap: %zu bytes", esp_get_free_heap_size());
   ```

3. **验证对象状态**:
   ```c
   if (!lv_obj_is_valid(obj)) {
       ESP_LOGE(TAG, "Invalid LVGL object");
   }
   ```

## 最佳实践

### ✅ 推荐做法
1. **使用画布版QR码**: 内存效率更高
2. **延迟初始化**: 确保系统稳定
3. **资源清理**: 及时释放不需要的对象
4. **内存监控**: 定期检查可用内存
5. **错误处理**: 提供完整的错误恢复机制

### ❌ 避免做法
1. 创建大量小LVGL对象
2. 忽略内存检查
3. 缺乏对象有效性验证
4. 没有错误恢复机制
5. 过短的初始化延迟

---

**开发完成时间**: 2025-08-02  
**系统状态**: ✅ 3屏导航系统完全实现  
**测试状态**: 🔄 待验证运行时稳定性和用户体验  
**推荐版本**: 内存安全的画布版本
