/**
 * @file screen_manager.h
 * @brief 3屏导航系统管理器头文件
 * <AUTHOR> Agent
 * @date 2025-08-02
 * 
 * 实现Screen 1(初始屏) -> Screen 2(菜单屏) -> Screen 3(QR码屏)的导航系统
 */

#ifndef SCREEN_MANAGER_H
#define SCREEN_MANAGER_H

#include "lvgl.h"
#include <stdbool.h>
#include <stdint.h>
#include "esp_wifi.h"
#include "esp_event.h"

#ifdef __cplusplus
extern "C" {
#endif

/*---- 屏幕类型定义 ----*/

/**
 * @brief 屏幕类型枚举
 */
typedef enum {
    SCREEN_INITIAL = 0,     // Screen 1: 初始屏幕，显示Left/Right按钮（增强WiFi功能）
    SCREEN_MENU,            // Screen 2: 菜单屏幕，显示4个按钮
    SCREEN_QRCODE,          // Screen 3: QR码屏幕，显示QR码
    SCREEN_PASSWORD,        // Screen 4: 密码验证屏幕
    SCREEN_WIFI_CONFIG,     // Screen 5: WiFi配置屏幕
    SCREEN_WIFI_PASSWORD,   // Screen 6: WiFi密码输入屏幕
    SCREEN_COUNT            // 屏幕总数
} screen_type_t;

/*---- WiFi管理相关定义 ----*/

/**
 * @brief WiFi连接状态枚举
 */
typedef enum {
    WIFI_STATUS_DISCONNECTED = 0,  // 未连接
    WIFI_STATUS_CONNECTING,        // 连接中
    WIFI_STATUS_CONNECTED,         // 已连接
    WIFI_STATUS_FAILED,            // 连接失败
    WIFI_STATUS_SCANNING           // 扫描中
} wifi_status_t;

/**
 * @brief WiFi热点信息结构体
 */
typedef struct {
    char ssid[33];              // WiFi名称 (最大32字符 + 终止符)
    int8_t rssi;                // 信号强度 (dBm)
    wifi_auth_mode_t authmode;  // 加密类型
    bool is_open;               // 是否开放网络
    uint8_t channel;            // 信道
} wifi_ap_info_t;

/**
 * @brief WiFi扫描结果结构体
 */
typedef struct {
    wifi_ap_info_t* ap_list;    // 热点列表
    uint16_t ap_count;          // 热点数量
    uint16_t max_count;         // 最大容量
    bool scan_complete;         // 扫描是否完成
} wifi_scan_result_t;

/**
 * @brief WiFi管理器结构体
 */
typedef struct {
    wifi_status_t status;           // 当前WiFi状态
    char connected_ssid[33];        // 当前连接的SSID
    int8_t signal_strength;         // 当前信号强度
    wifi_scan_result_t scan_result; // 扫描结果
    bool auto_connect_enabled;      // 是否启用自动连接
    char saved_ssid[33];            // 保存的SSID
    char saved_password[65];        // 保存的密码 (最大64字符 + 终止符)
    char input_password[65];        // 当前输入的密码缓冲区
    int input_password_length;      // 当前输入密码的长度
    char custom_ssid[33];           // 自定义SSID输入缓冲区（用于隐藏网络）
    int custom_ssid_length;         // 自定义SSID长度
    bool is_custom_network_mode;    // 是否为自定义网络模式（隐藏网络）
} wifi_manager_t;

/*---- 密码验证相关定义 ----*/

#define ADMIN_PASSWORD "0001"
#define MAX_PASSWORD_ATTEMPTS 3
#define LOCKOUT_DURATION_MS (5 * 60 * 1000)  // 5分钟锁定时间

/**
 * @brief 密码验证管理器结构体
 */
typedef struct {
    uint8_t failed_attempts;        // 失败尝试次数
    uint32_t lockout_start_time;    // 锁定开始时间
    bool is_locked;                 // 是否被锁定
    char input_buffer[5];           // 输入缓冲区 (4位密码 + 终止符)
    uint8_t input_length;           // 当前输入长度
} password_manager_t;

/**
 * @brief 屏幕状态结构体
 */
typedef struct {
    screen_type_t current_screen;       // 当前屏幕
    screen_type_t previous_screen;      // 上一个屏幕
    lv_obj_t* screen_objects[SCREEN_COUNT];  // 各屏幕的根对象
    lv_obj_t* qr_canvas;               // QR码画布对象
    wifi_manager_t wifi_manager;       // WiFi管理器
    password_manager_t password_manager; // 密码验证管理器
    lv_obj_t* wifi_status_indicator;   // WiFi状态指示器对象
    lv_timer_t* wifi_status_timer;     // WiFi状态更新定时器
    lv_timer_t* wifi_connection_timeout_timer;  // WiFi连接超时定时器
    lv_timer_t* wifi_reconnect_timer;  // WiFi自动重连定时器
    uint32_t reconnect_attempt_count;  // 重连尝试次数
    uint32_t title_click_count;        // 标题点击次数（用于双击检测）
    uint32_t last_title_click_time;    // 上次标题点击时间
    bool is_initialized;               // 是否已初始化
} screen_manager_t;

/*---- 公共函数声明 ----*/

/**
 * @brief 初始化屏幕管理器
 * @return true if successful, false otherwise
 */
bool screen_manager_init(void);

/**
 * @brief 获取屏幕管理器实例
 * @return 屏幕管理器指针
 */
screen_manager_t* screen_manager_get_instance(void);

/**
 * @brief 切换到指定屏幕
 * @param target_screen 目标屏幕类型
 * @return true if successful, false otherwise
 */
bool screen_manager_switch_to(screen_type_t target_screen);

/**
 * @brief 获取当前屏幕类型
 * @return 当前屏幕类型
 */
screen_type_t screen_manager_get_current_screen(void);

/**
 * @brief 返回上一个屏幕
 * @return true if successful, false otherwise
 */
bool screen_manager_go_back(void);

/**
 * @brief 清理屏幕管理器资源
 */
void screen_manager_cleanup(void);

/*---- Screen 1: 初始屏幕函数 ----*/

/**
 * @brief 创建初始屏幕 (Screen 1)
 * @return 屏幕对象指针或NULL
 */
lv_obj_t* screen_create_initial(void);

/**
 * @brief 初始屏幕按钮事件处理器
 * @param e 事件对象
 */
void screen_initial_button_handler(lv_event_t* e);

/*---- Screen 2: 菜单屏幕函数 ----*/

/**
 * @brief 创建菜单屏幕 (Screen 2)
 * @return 屏幕对象指针或NULL
 */
lv_obj_t* screen_create_menu(void);

/**
 * @brief 菜单屏幕按钮事件处理器
 * @param e 事件对象
 */
void screen_menu_button_handler(lv_event_t* e);

/*---- Screen 3: QR码屏幕函数 ----*/

/**
 * @brief 创建QR码屏幕 (Screen 3)
 * @return 屏幕对象指针或NULL
 */
lv_obj_t* screen_create_qrcode(void);

/**
 * @brief QR码屏幕触摸事件处理器
 * @param e 事件对象
 */
void screen_qrcode_touch_handler(lv_event_t* e);

/*---- Screen 4: 密码验证屏幕函数 ----*/

/**
 * @brief 创建密码验证屏幕 (Screen 4)
 * @return 屏幕对象指针或NULL
 */
lv_obj_t* screen_create_password(void);

/**
 * @brief 密码验证屏幕数字键盘事件处理器
 * @param e 事件对象
 */
void screen_password_keypad_handler(lv_event_t* e);

/**
 * @brief 密码验证屏幕控制按钮事件处理器
 * @param e 事件对象
 */
void screen_password_control_handler(lv_event_t* e);

/*---- Screen 5: WiFi配置屏幕函数 ----*/

/**
 * @brief 创建WiFi配置屏幕 (Screen 5)
 * @return 屏幕对象指针或NULL
 */
lv_obj_t* screen_create_wifi_config(void);

/**
 * @brief WiFi配置屏幕WiFi列表事件处理器
 * @param e 事件对象
 */
void screen_wifi_config_list_handler(lv_event_t* e);

/**
 * @brief WiFi配置屏幕控制按钮事件处理器
 * @param e 事件对象
 */
void screen_wifi_config_control_handler(lv_event_t* e);

/*---- Screen 6: WiFi密码输入屏幕函数 ----*/

/**
 * @brief 创建WiFi密码输入屏幕 (Screen 6)
 * @param selected_ssid 选中的WiFi SSID
 * @return 屏幕对象指针或NULL
 */
lv_obj_t* screen_create_wifi_password(const char* selected_ssid);

/**
 * @brief WiFi密码输入屏幕键盘事件处理器
 * @param e 事件对象
 */
void screen_wifi_password_keyboard_handler(lv_event_t* e);

/**
 * @brief WiFi密码输入屏幕控制按钮事件处理器
 * @param e 事件对象
 */
void screen_wifi_password_control_handler(lv_event_t* e);

/*---- 辅助函数 ----*/

/**
 * @brief 创建居中按钮
 * @param parent 父对象
 * @param text 按钮文本
 * @param x_offset X偏移量
 * @param y_offset Y偏移量
 * @param width 按钮宽度
 * @param height 按钮高度
 * @param event_handler 事件处理函数
 * @param user_data 用户数据
 * @return 按钮对象指针或NULL
 */
lv_obj_t* create_centered_button(lv_obj_t* parent, const char* text, 
                                lv_coord_t x_offset, lv_coord_t y_offset,
                                lv_coord_t width, lv_coord_t height,
                                lv_event_cb_t event_handler, void* user_data);

/**
 * @brief 安全删除屏幕对象
 * @param screen 屏幕对象指针
 */
void safe_delete_screen(lv_obj_t** screen);

/*---- 调试和日志函数 ----*/

/**
 * @brief 打印屏幕管理器状态
 */
void screen_manager_print_status(void);

/**
 * @brief 获取屏幕类型名称
 * @param screen_type 屏幕类型
 * @return 屏幕名称字符串
 */
const char* screen_get_type_name(screen_type_t screen_type);

/*---- WiFi管理函数 ----*/

/**
 * @brief 初始化WiFi管理器
 * @return ESP_OK if successful
 */
esp_err_t wifi_manager_init(void);

/**
 * @brief 开始WiFi扫描
 * @return ESP_OK if successful
 */
esp_err_t wifi_start_scan(void);

/**
 * @brief 连接到指定WiFi
 * @param ssid WiFi名称
 * @param password WiFi密码
 * @return ESP_OK if successful
 */
esp_err_t wifi_connect(const char* ssid, const char* password);

/**
 * @brief 断开WiFi连接
 * @return ESP_OK if successful
 */
esp_err_t wifi_disconnect(void);

/**
 * @brief 获取WiFi状态
 * @return 当前WiFi状态
 */
wifi_status_t wifi_get_status(void);

/**
 * @brief 获取信号强度
 * @return 信号强度 (dBm)
 */
int8_t wifi_get_signal_strength(void);

/**
 * @brief 获取扫描结果
 * @param results 扫描结果结构体指针
 * @return ESP_OK if successful
 */
esp_err_t wifi_get_scan_results(wifi_scan_result_t* results);

/**
 * @brief 释放扫描结果内存
 * @param results 扫描结果结构体指针
 */
void wifi_free_scan_results(wifi_scan_result_t* results);

/**
 * @brief 保存WiFi凭据到NVS
 * @param ssid WiFi名称
 * @param password WiFi密码
 * @return ESP_OK if successful
 */
esp_err_t save_wifi_credentials(const char* ssid, const char* password);

/**
 * @brief 从NVS加载WiFi凭据
 * @param ssid WiFi名称缓冲区
 * @param password WiFi密码缓冲区
 * @return ESP_OK if successful
 */
esp_err_t load_wifi_credentials(char* ssid, char* password);

/**
 * @brief 清除保存的WiFi凭据
 * @return ESP_OK if successful
 */
esp_err_t clear_wifi_credentials(void);

/*---- 密码验证函数 ----*/

/**
 * @brief 验证管理员密码
 * @param input 输入的密码
 * @return true if password is correct
 */
bool validate_admin_password(const char* input);

/**
 * @brief 检查密码是否被锁定
 * @return true if locked
 */
bool is_password_locked(void);

/**
 * @brief 重置密码尝试次数
 */
void reset_password_attempts(void);

/**
 * @brief 增加失败尝试次数
 */
void increment_failed_attempts(void);

/**
 * @brief 获取剩余尝试次数
 * @return 剩余尝试次数
 */
uint8_t get_remaining_attempts(void);

/*---- WiFi状态指示器函数 ----*/

/**
 * @brief 创建WiFi状态指示器
 * @param parent 父对象
 * @return 指示器对象指针或NULL
 */
lv_obj_t* create_wifi_status_indicator(lv_obj_t* parent);

/**
 * @brief 更新WiFi状态指示器
 * @param indicator 指示器对象
 * @param status WiFi状态
 * @param signal_strength 信号强度
 */
void update_wifi_status_indicator(lv_obj_t* indicator, wifi_status_t status, int8_t signal_strength);

/**
 * @brief WiFi状态更新定时器回调
 * @param timer 定时器对象
 */
void wifi_status_timer_callback(lv_timer_t* timer);

/**
 * @brief 启动时自动连接到保存的WiFi
 * @return ESP_OK 成功，其他值表示失败
 */
esp_err_t wifi_auto_connect_on_boot(void);

/**
 * @brief 更新WiFi列表显示
 */
void update_wifi_list_display(void);

/**
 * @brief 处理WiFi连接结果
 * @param success 连接是否成功
 * @param error_msg 错误消息（如果失败）
 */
void handle_wifi_connection_result(bool success, const char* error_msg);

/*---- 按钮事件处理函数 ----*/

/**
 * @brief WIFI按钮事件处理函数
 * @param e 事件对象
 */
void screen_wifi_button_handler(lv_event_t* e);

/**
 * @brief WiFi密码roller事件处理函数
 * @param e 事件对象
 */
void screen_wifi_password_roller_handler(lv_event_t* e);

/**
 * @brief WiFi密码字符添加事件处理函数
 * @param e 事件对象
 */
void screen_wifi_password_char_handler(lv_event_t* e);

/**
 * @brief WiFi密码清除事件处理函数
 * @param e 事件对象
 */
void screen_wifi_password_clear_handler(lv_event_t* e);

/**
 * @brief 更新密码显示
 * @param screen 屏幕对象
 */
void update_password_display(lv_obj_t* screen);

/**
 * @brief 更新WiFi密码显示（新设计）
 * @param screen 屏幕对象
 */
void update_wifi_password_display(lv_obj_t* screen);

/**
 * @brief 安全地查找状态标签
 * @param screen 屏幕对象
 * @return 状态标签对象或NULL
 */
lv_obj_t* find_status_label(lv_obj_t* screen);

/**
 * @brief 安全地查找连接按钮
 * @param screen 屏幕对象
 * @return 连接按钮对象或NULL
 */
lv_obj_t* find_connect_button(lv_obj_t* screen);

/**
 * @brief WiFi连接成功后的定时器回调
 * @param timer 定时器对象
 */
void wifi_connection_success_timer_cb(lv_timer_t* timer);

/**
 * @brief 延迟清理屏幕对象的定时器回调
 * @param timer 定时器对象
 */
void delayed_screen_cleanup_timer_cb(lv_timer_t* timer);

/**
 * @brief WiFi连接超时定时器回调
 * @param timer 定时器对象
 */
void wifi_connection_timeout_timer_cb(lv_timer_t* timer);

/**
 * @brief WiFi自动重连定时器回调
 * @param timer 定时器对象
 */
void wifi_auto_reconnect_timer_cb(lv_timer_t* timer);

/**
 * @brief 启动WiFi自动重连机制
 * @param delay_ms 延迟时间（毫秒）
 */
void start_wifi_auto_reconnect(uint32_t delay_ms);

/**
 * @brief 停止WiFi自动重连机制
 */
void stop_wifi_auto_reconnect(void);

/**
 * @brief WiFi配置屏幕标题双击事件处理器
 */
void screen_wifi_config_title_handler(lv_event_t* e);

/**
 * @brief 创建增强版WiFi密码输入屏幕（支持自定义SSID）
 * @param ssid 预设的SSID（如果为NULL则为自定义模式）
 * @return 创建的屏幕对象
 */
lv_obj_t* screen_create_wifi_password_enhanced(const char* ssid);

/**
 * @brief 自定义SSID字符添加事件处理器
 */
void screen_wifi_custom_ssid_char_handler(lv_event_t* e);

/**
 * @brief 自定义SSID清除事件处理器
 */
void screen_wifi_custom_ssid_clear_handler(lv_event_t* e);

/**
 * @brief 密码验证roller事件处理函数
 * @param e 事件对象
 */
void screen_password_roller_handler(lv_event_t* e);

/**
 * @brief 更新密码验证显示
 * @param screen 屏幕对象
 */
void update_password_verification_display(lv_obj_t* screen);



#ifdef __cplusplus
}
#endif

#endif // SCREEN_MANAGER_H
