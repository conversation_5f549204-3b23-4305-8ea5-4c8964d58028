# ESP32-S3 LVGL 3屏导航系统测试指南

## 测试准备

### 硬件要求
- ESP32-S3开发板
- 240x320像素显示屏 (ZX2D80CE02S-2432)
- 触摸功能正常工作

### 软件要求
- ESP-IDF 4.4+
- LVGL 8.3.1
- 项目正确编译无错误

## 功能测试清单

### ✅ 1. 系统启动测试

**测试步骤**:
1. 烧录固件到ESP32-S3
2. 重启设备
3. 观察串口日志和显示屏

**预期结果**:
```
I (xxxx) QMSD-MAIN: Starting 3-screen navigation system...
I (xxxx) QMSD-MAIN: GUI user initialization complete - navigation system will start shortly
I (xxxx) QMSD-MAIN: Initializing 3-screen navigation system...
I (xxxx) SCREEN_MGR: Initializing screen manager...
I (xxxx) SCREEN_MGR: Creating initial screen...
I (xxxx) SCREEN_MGR: Initial screen created successfully
I (xxxx) SCREEN_MGR: Screen manager initialized successfully
```

**显示内容**:
- 深蓝色背景 (#003366)
- 白色标题 "Welcome Screen"
- 两个绿色按钮: "Left Button" 和 "Right Button"
- 按钮水平居中排列

### ✅ 2. Screen 1 → Screen 2 导航测试

**测试步骤**:
1. 在初始屏幕点击 "Left Button"
2. 观察屏幕切换和日志

**预期结果**:
```
I (xxxx) SCREEN_MGR: Initial screen button 0 clicked
I (xxxx) SCREEN_MGR: Switching from Initial Screen to Menu Screen
I (xxxx) SCREEN_MGR: Creating menu screen...
I (xxxx) SCREEN_MGR: Menu screen created successfully
I (xxxx) SCREEN_MGR: Successfully switched to Menu Screen
```

**显示内容**:
- 深绿色背景 (#2E7D32)
- 白色标题 "Menu Screen"
- 2x2网格的4个绿色按钮: "Button 1", "Button 2", "Button 3", "Button 4"

**重复测试**: 点击 "Right Button" 应该有相同结果

### ✅ 3. Screen 2 → Screen 3 导航测试

**测试步骤**:
1. 在菜单屏幕点击任一按钮 (如 "Button 1")
2. 观察QR码生成和显示

**预期结果**:
```
I (xxxx) SCREEN_MGR: Menu screen button 1 clicked
I (xxxx) SCREEN_MGR: Switching from Menu Screen to QR Code Screen
I (xxxx) SCREEN_MGR: Creating QR code screen...
I (xxxx) QRCODE_SAFE: Creating canvas QR code for: https://render.alipay.hk/p/s/hkwallet/landing/qrcode?code=281004040598XXwl31cYpzUaA50b9ga0dFkQ
I (xxxx) QRCODE_SAFE: Free heap before QR creation: xxxxx bytes
I (xxxx) QRCODE_SAFE: Generated QR code size: xx x xx
I (xxxx) QRCODE_SAFE: Canvas size: xxx x xxx, scale: x
I (xxxx) QRCODE_SAFE: Using PSRAM for canvas buffer
I (xxxx) QRCODE_SAFE: Canvas QR code created successfully
I (xxxx) SCREEN_MGR: QR code screen created successfully
```

**显示内容**:
- 深蓝紫色背景 (#1A237E)
- 白色标题 "QR Code Screen"
- 居中显示的支付宝QR码
- 底部灰色提示 "Touch QR code to return"

### ✅ 4. Screen 3 → Screen 1 返回测试

**测试步骤**:
1. 在QR码屏幕点击QR码区域
2. 观察返回到初始屏幕

**预期结果**:
```
I (xxxx) SCREEN_MGR: QR code touched, returning to initial screen
I (xxxx) SCREEN_MGR: Switching from QR Code Screen to Initial Screen
I (xxxx) QRCODE_SAFE: Canvas buffer freed
I (xxxx) QRCODE_SAFE: Canvas object deleted
I (xxxx) SCREEN_MGR: Successfully switched to Initial Screen
```

**显示内容**: 返回到初始屏幕，显示Left/Right按钮

### ✅ 5. 内存管理测试

**测试步骤**:
1. 多次在屏幕间切换 (至少10次完整循环)
2. 监控内存使用情况

**预期结果**:
- 内存使用稳定，无明显泄漏
- QR码创建/销毁时内存正确释放
- 系统长时间运行稳定

**监控命令**:
```c
// 在代码中添加内存监控
size_t free_heap = esp_get_free_heap_size();
ESP_LOGI(TAG, "Free heap: %zu bytes", free_heap);
```

### ✅ 6. 触摸响应测试

**测试步骤**:
1. 快速连续点击按钮
2. 测试边界区域点击
3. 测试长按和短按

**预期结果**:
- 所有按钮响应正常
- 无重复触发
- 边界点击准确识别

### ✅ 7. 错误恢复测试

**测试步骤**:
1. 在内存不足情况下测试
2. 模拟LVGL对象创建失败
3. 测试异常情况处理

**预期结果**:
- 系统优雅处理错误
- 提供清晰的错误日志
- 不会崩溃或重启

## 性能测试

### 📊 内存使用基准

**正常运行时内存使用**:
- 初始屏幕: ~XXX KB
- 菜单屏幕: ~XXX KB  
- QR码屏幕: ~XXX KB (包含画布缓冲区)

**QR码内存对比**:
- 旧版本 (小对象): 137KB + 内存碎片
- 新版本 (画布): 51KB 连续内存

### ⚡ 响应时间基准

- 屏幕切换延迟: < 100ms
- 按钮响应时间: < 50ms
- QR码生成时间: < 500ms
- 系统启动时间: ~2秒 (包含初始化延迟)

### 🔄 稳定性测试

**长时间运行测试**:
1. 连续运行24小时
2. 每分钟自动切换屏幕
3. 监控内存泄漏和系统稳定性

**压力测试**:
1. 快速连续切换屏幕 (1000次)
2. 同时创建多个QR码
3. 内存不足情况下的行为

## 故障排除

### 🚨 常见问题及解决方案

#### 1. 屏幕不显示或显示异常
**可能原因**:
- LVGL初始化时序问题
- 显示驱动配置错误
- 内存不足

**解决方案**:
```bash
# 检查LVGL配置
idf.py menuconfig
# 增加初始化延迟
lv_timer_create(navigation_system_init_timer_cb, 3000, NULL);
```

#### 2. 触摸无响应
**可能原因**:
- 触摸驱动未正确初始化
- 事件处理器未正确注册
- LVGL对象标志设置错误

**解决方案**:
```c
// 确保对象可点击
lv_obj_add_flag(obj, LV_OBJ_FLAG_CLICKABLE);
// 检查事件处理器
lv_obj_add_event_cb(obj, handler, LV_EVENT_CLICKED, NULL);
```

#### 3. 内存不足错误
**可能原因**:
- PSRAM配置错误
- 内存泄漏
- QR码缓冲区过大

**解决方案**:
```bash
# 启用PSRAM
idf.py menuconfig → Component config → ESP32S3-Specific → Support for external, SPI-connected RAM
# 监控内存使用
esp_get_free_heap_size()
```

#### 4. QR码显示异常
**可能原因**:
- 画布缓冲区分配失败
- QR码数据过长
- 颜色格式不匹配

**解决方案**:
```c
// 检查画布创建
if (!canvas_buf) {
    ESP_LOGE(TAG, "Canvas buffer allocation failed");
    return NULL;
}
// 验证QR码数据长度
if (strlen(text) > 200) {
    ESP_LOGW(TAG, "QR code text too long");
}
```

### 📝 调试技巧

#### 1. 启用详细日志
```c
esp_log_level_set("SCREEN_MGR", ESP_LOG_DEBUG);
esp_log_level_set("QRCODE_SAFE", ESP_LOG_DEBUG);
esp_log_level_set("QMSD-MAIN", ESP_LOG_DEBUG);
```

#### 2. 内存监控
```c
// 定期打印内存状态
void print_memory_info() {
    size_t free_heap = esp_get_free_heap_size();
    size_t min_free = esp_get_minimum_free_heap_size();
    ESP_LOGI(TAG, "Free: %zu, Min: %zu", free_heap, min_free);
}
```

#### 3. 对象验证
```c
// 验证LVGL对象有效性
if (!lv_obj_is_valid(obj)) {
    ESP_LOGE(TAG, "Invalid LVGL object detected");
    return;
}
```

## 测试报告模板

### 测试环境
- **硬件**: ESP32-S3 + 240x320显示屏
- **软件版本**: ESP-IDF x.x.x, LVGL 8.3.1
- **测试日期**: YYYY-MM-DD
- **测试人员**: [姓名]

### 测试结果
- [ ] 系统启动正常
- [ ] Screen 1 → Screen 2 导航正常
- [ ] Screen 2 → Screen 3 导航正常  
- [ ] Screen 3 → Screen 1 返回正常
- [ ] 内存管理正常
- [ ] 触摸响应正常
- [ ] 错误恢复正常

### 性能数据
- 启动时间: _____ 秒
- 屏幕切换延迟: _____ ms
- QR码生成时间: _____ ms
- 内存使用峰值: _____ KB

### 问题记录
1. [问题描述] - [解决方案] - [状态]
2. [问题描述] - [解决方案] - [状态]

### 总体评价
- ✅ 通过 / ❌ 失败
- 备注: [详细说明]

---

**测试指南版本**: v1.0  
**最后更新**: 2025-08-02  
**适用项目**: ESP32-S3 LVGL 3屏导航系统
