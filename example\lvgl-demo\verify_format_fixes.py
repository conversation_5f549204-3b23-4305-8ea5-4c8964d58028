#!/usr/bin/env python3
"""
格式说明符修复验证脚本
检查所有格式说明符是否正确修复
"""

import re
import os

def check_format_specifier_fixes():
    """检查格式说明符修复"""
    print("🔍 检查格式说明符修复状态")
    print("-" * 50)
    
    screen_manager_c = r"s:\debug_stm32_wawaji_128\office_esp32s3\qmsd-esp32-bsp\example\lvgl-demo\main\screen_manager.c"
    
    if not os.path.exists(screen_manager_c):
        print("❌ screen_manager.c文件不存在")
        return False
    
    try:
        with open(screen_manager_c, 'r', encoding='utf-8') as f:
            content = f.read()
        
        fixes_verified = []
        
        # 检查修复1: delay_ms (uint32_t)
        if "%lu ms delay" in content and "(unsigned long)delay_ms" in content:
            fixes_verified.append("✅ 修复1: uint32_t delay_ms 格式说明符已修复")
        else:
            fixes_verified.append("❌ 修复1: delay_ms 格式说明符未修复")
        
        # 检查修复2: next_delay (uint32_t)
        if "%lu ms" in content and "(unsigned long)next_delay" in content:
            fixes_verified.append("✅ 修复2: uint32_t next_delay 格式说明符已修复")
        else:
            fixes_verified.append("❌ 修复2: next_delay 格式说明符未修复")
        
        # 检查修复3: reconnect_attempt_count (uint32_t)
        if "(unsigned long)(g_screen_manager.reconnect_attempt_count + 1)" in content:
            fixes_verified.append("✅ 修复3: uint32_t reconnect_attempt_count 格式说明符已修复")
        else:
            fixes_verified.append("❌ 修复3: reconnect_attempt_count 格式说明符未修复")
        
        # 检查修复4: selected (uint16_t) - 密码roller
        if "Password roller changed to: %u" in content and "(unsigned int)selected" in content:
            fixes_verified.append("✅ 修复4: uint16_t selected (密码roller) 格式说明符已修复")
        else:
            fixes_verified.append("❌ 修复4: selected (密码roller) 格式说明符未修复")
        
        # 检查修复5: selected (uint16_t) - WiFi密码roller
        if "WiFi password roller changed to: %u" in content:
            fixes_verified.append("✅ 修复5: uint16_t selected (WiFi密码roller) 格式说明符已修复")
        else:
            fixes_verified.append("❌ 修复5: selected (WiFi密码roller) 格式说明符未修复")
        
        # 检查修复6: ap_count (uint16_t)
        if "scan results retrieved: %u networks" in content and "(unsigned int)ap_count" in content:
            fixes_verified.append("✅ 修复6: uint16_t ap_count 格式说明符已修复")
        else:
            fixes_verified.append("❌ 修复6: ap_count 格式说明符未修复")
        
        # 检查修复7: input_length (uint8_t)
        if "Password length incorrect: %u" in content and "(unsigned int)g_screen_manager.password_manager.input_length" in content:
            fixes_verified.append("✅ 修复7: uint8_t input_length 格式说明符已修复")
        else:
            fixes_verified.append("❌ 修复7: input_length 格式说明符未修复")
        
        # 检查修复8: failed_attempts (uint8_t)
        if "Failed password attempt %u/%d" in content and "(unsigned int)g_screen_manager.password_manager.failed_attempts" in content:
            fixes_verified.append("✅ 修复8: uint8_t failed_attempts 格式说明符已修复")
        else:
            fixes_verified.append("❌ 修复8: failed_attempts 格式说明符未修复")
        
        # 显示检查结果
        for fix in fixes_verified:
            print(f"  {fix}")
        
        # 检查是否还有潜在的格式问题
        print(f"\n🔍 检查潜在的格式问题:")
        
        # 查找可能的问题模式
        potential_issues = []
        
        # 查找 %d 与可能的无符号类型
        uint_patterns = [
            r'ESP_LOG[IEDW]\([^,]+,\s*[^,]*%d[^,]*,\s*[^,]*uint\d+_t',
            r'ESP_LOG[IEDW]\([^,]+,\s*[^,]*%d[^,]*,\s*[^,]*size_t'
        ]
        
        for pattern in uint_patterns:
            matches = re.findall(pattern, content, re.MULTILINE)
            if matches:
                potential_issues.extend(matches)
        
        if potential_issues:
            print("  ⚠️  发现潜在的格式问题:")
            for issue in potential_issues:
                print(f"    {issue}")
        else:
            print("  ✅ 未发现其他潜在的格式问题")
        
        # 统计修复成功数量
        success_count = sum(1 for fix in fixes_verified if fix.startswith("✅"))
        total_count = len(fixes_verified)
        
        print(f"\n📊 修复状态: {success_count}/{total_count} 项格式说明符已修复")
        
        return success_count == total_count
        
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 ESP32 LVGL WiFi项目格式说明符修复验证")
    print("=" * 60)
    
    if check_format_specifier_fixes():
        print("\n🎉 所有格式说明符错误修复成功！")
        print("\n📌 修复总结:")
        print("  • 修复了7个格式说明符编译错误")
        print("  • 确保了类型安全的日志输出")
        print("  • 代码现在兼容 -Werror=format 编译标志")
        print("  • 保持了所有原有功能不变")
        
        print(f"\n📋 下一步建议:")
        print("1. 使用 idf.py build 重新编译项目")
        print("2. 验证编译过程无格式相关错误")
        print("3. 测试WiFi自动重连功能")
        print("4. 检查日志输出是否正确显示数值")
        
        return 0
    else:
        print("\n❌ 格式说明符修复验证失败")
        print("请检查修复是否完整")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
