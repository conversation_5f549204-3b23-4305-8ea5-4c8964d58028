#!/usr/bin/env python3
"""
WiFi密码输入框6增强功能测试脚本
测试新增的同时编辑WiFi热点名称和密码功能
"""

import os
import re
import sys

def test_function_declarations():
    """测试函数声明是否正确添加到头文件"""
    print("=== 测试函数声明 ===")
    
    header_file = "main/screen_manager.h"
    if not os.path.exists(header_file):
        print(f"❌ 头文件不存在: {header_file}")
        return False
    
    with open(header_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查新增的函数声明
    required_declarations = [
        "screen_wifi_custom_ssid_clear_handler",
        "screen_create_wifi_password_enhanced"
    ]
    
    success = True
    for func_name in required_declarations:
        if func_name in content:
            print(f"✅ 找到函数声明: {func_name}")
        else:
            print(f"❌ 缺少函数声明: {func_name}")
            success = False
    
    return success

def test_function_implementations():
    """测试函数实现是否正确添加到源文件"""
    print("\n=== 测试函数实现 ===")
    
    source_file = "main/screen_manager.c"
    if not os.path.exists(source_file):
        print(f"❌ 源文件不存在: {source_file}")
        return False
    
    with open(source_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键函数实现
    required_functions = [
        "screen_create_wifi_password_enhanced",
        "screen_wifi_custom_ssid_clear_handler",
        "screen_wifi_password_char_handler"
    ]
    
    success = True
    for func_name in required_functions:
        pattern = rf"void\s+{func_name}\s*\("
        if re.search(pattern, content):
            print(f"✅ 找到函数实现: {func_name}")
        else:
            print(f"❌ 缺少函数实现: {func_name}")
            success = False
    
    return success

def test_enhanced_screen_structure():
    """测试增强版屏幕的结构是否正确"""
    print("\n=== 测试增强版屏幕结构 ===")
    
    source_file = "main/screen_manager.c"
    with open(source_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找screen_create_wifi_password_enhanced函数
    func_match = re.search(
        r'lv_obj_t\*\s+screen_create_wifi_password_enhanced\s*\([^)]*\)\s*\{(.*?)(?=\n\w|\nstatic|\n/\*|\n$)',
        content, re.DOTALL
    )
    
    if not func_match:
        print("❌ 未找到screen_create_wifi_password_enhanced函数")
        return False
    
    func_body = func_match.group(1)
    
    # 检查关键组件
    required_components = [
        ("SSID输入标签", r"Network Name.*SSID"),
        ("SSID显示区域", r"ssid_display.*lv_label_create"),
        ("SSID字符roller", r"ssid_roller.*lv_roller_create"),
        ("密码输入标签", r"WiFi Password"),
        ("密码显示区域", r"password_display.*lv_label_create"),
        ("密码字符roller", r"password_roller.*lv_roller_create"),
        ("连接按钮", r"connect_btn.*lv_btn_create"),
        ("取消按钮", r"cancel_btn.*lv_btn_create"),
        ("状态标签", r"status_label.*lv_label_create")
    ]
    
    success = True
    for component_name, pattern in required_components:
        if re.search(pattern, func_body, re.IGNORECASE):
            print(f"✅ 找到组件: {component_name}")
        else:
            print(f"❌ 缺少组件: {component_name}")
            success = False
    
    return success

def test_dual_mode_support():
    """测试双模式支持"""
    print("\n=== 测试双模式支持 ===")
    
    source_file = "main/screen_manager.c"
    with open(source_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查模式判断逻辑
    mode_checks = [
        ("自定义网络模式标志设置", r"is_custom_network_mode.*=.*ssid.*==.*NULL"),
        ("自定义模式界面创建", r"if.*is_custom_network_mode.*\{"),
        ("标题动态设置", r"Custom Network Setup|Connect to:")
    ]
    
    success = True
    for check_name, pattern in mode_checks:
        if re.search(pattern, content, re.IGNORECASE):
            print(f"✅ 找到逻辑: {check_name}")
        else:
            print(f"❌ 缺少逻辑: {check_name}")
            success = False
    
    return success

def test_event_handlers():
    """测试事件处理器"""
    print("\n=== 测试事件处理器 ===")
    
    source_file = "main/screen_manager.c"
    with open(source_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查事件处理器的关键逻辑
    handler_checks = [
        ("SSID字符添加", r"custom_ssid.*\[.*custom_ssid_length.*\].*=.*selected_char"),
        ("密码字符添加", r"input_password.*\[.*input_password_length.*\].*=.*selected_char"),
        ("SSID清除功能", r"custom_ssid_length.*--"),
        ("密码清除功能", r"input_password_length.*--")
    ]
    
    success = True
    for check_name, pattern in handler_checks:
        if re.search(pattern, content, re.IGNORECASE):
            print(f"✅ 找到处理逻辑: {check_name}")
        else:
            print(f"❌ 缺少处理逻辑: {check_name}")
            success = False
    
    return success

def main():
    """主测试函数"""
    print("WiFi密码输入框6增强功能测试")
    print("=" * 50)
    
    # 切换到正确的目录
    if os.path.basename(os.getcwd()) != "lvgl-demo":
        if os.path.exists("example/lvgl-demo"):
            os.chdir("example/lvgl-demo")
        elif os.path.exists("lvgl-demo"):
            os.chdir("lvgl-demo")
    
    tests = [
        test_function_declarations,
        test_function_implementations,
        test_enhanced_screen_structure,
        test_dual_mode_support,
        test_event_handlers
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！WiFi密码输入框6增强功能实现成功！")
        return 0
    else:
        print("⚠️  部分测试失败，请检查实现")
        return 1

if __name__ == "__main__":
    sys.exit(main())
