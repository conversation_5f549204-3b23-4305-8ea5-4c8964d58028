#!/usr/bin/env python3
"""
ESP32-S3 LVGL 3屏导航系统修复验证脚本

验证以下修复：
1. 画面1按键文字修改（"Left Button" → "Left", "Right Button" → "Right"）
2. QR码重复显示问题修复
3. 滚动条移除
"""

import os
import sys
import re

def check_button_text_fix():
    """检查按键文字修复"""
    print("🔍 检查按键文字修复...")
    
    screen_manager_file = "main/screen_manager.c"
    if not os.path.exists(screen_manager_file):
        print(f"❌ 文件不存在: {screen_manager_file}")
        return False
    
    with open(screen_manager_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否已修复为简短文字
    if '"Left"' in content and '"Right"' in content:
        print("✅ 发现简化后的按键文字: 'Left' 和 'Right'")
        
        # 检查是否还有旧的长文字
        if '"Left Button"' in content or '"Right Button"' in content:
            print("❌ 仍然存在旧的长按键文字")
            return False
        else:
            print("✅ 已移除旧的长按键文字")
            return True
    else:
        print("❌ 未找到新的按键文字")
        return False

def check_qr_code_fix():
    """检查QR码重复显示修复"""
    print("\n🔍 检查QR码重复显示修复...")
    
    screen_manager_file = "main/screen_manager.c"
    if not os.path.exists(screen_manager_file):
        print(f"❌ 文件不存在: {screen_manager_file}")
        return False
    
    with open(screen_manager_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查QR码屏幕重建逻辑
    checks = [
        ("QR码屏幕特殊处理", "target_screen == SCREEN_QRCODE"),
        ("强制删除现有屏幕", "safe_delete_screen(&g_screen_manager.screen_objects[SCREEN_QRCODE])"),
        ("重新创建QR码屏幕", "screen_create_qrcode()"),
        ("重建日志", "QR code screen recreated for reliable display")
    ]
    
    all_passed = True
    for check_name, pattern in checks:
        if pattern in content:
            print(f"✅ {check_name}: 发现相关代码")
        else:
            print(f"❌ {check_name}: 未找到相关代码")
            all_passed = False
    
    return all_passed

def check_scrollbar_removal():
    """检查滚动条移除"""
    print("\n🔍 检查滚动条移除...")
    
    screen_manager_file = "main/screen_manager.c"
    if not os.path.exists(screen_manager_file):
        print(f"❌ 文件不存在: {screen_manager_file}")
        return False
    
    with open(screen_manager_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查滚动条禁用代码
    scrollbar_checks = [
        "lv_obj_clear_flag(screen, LV_OBJ_FLAG_SCROLLABLE)",
        "lv_obj_set_scrollbar_mode(screen, LV_SCROLLBAR_MODE_OFF)"
    ]
    
    # 统计每个屏幕创建函数中的滚动条禁用代码
    screen_functions = ["screen_create_initial", "screen_create_menu", "screen_create_qrcode"]
    
    all_passed = True
    for func_name in screen_functions:
        # 找到函数的开始和结束
        func_pattern = rf"{func_name}\s*\([^)]*\)\s*\{{"
        func_match = re.search(func_pattern, content)
        
        if func_match:
            func_start = func_match.end()
            # 简单地查找下一个函数或文件结束
            next_func_pattern = r"\n\w+\s+\w+\s*\([^)]*\)\s*\{"
            next_func_match = re.search(next_func_pattern, content[func_start:])
            
            if next_func_match:
                func_end = func_start + next_func_match.start()
            else:
                func_end = len(content)
            
            func_content = content[func_start:func_end]
            
            # 检查此函数中是否有滚动条禁用代码
            has_scrollbar_disable = all(check in func_content for check in scrollbar_checks)
            
            if has_scrollbar_disable:
                print(f"✅ {func_name}: 已添加滚动条禁用代码")
            else:
                print(f"❌ {func_name}: 缺少滚动条禁用代码")
                all_passed = False
        else:
            print(f"❌ 未找到函数: {func_name}")
            all_passed = False
    
    return all_passed

def check_code_quality():
    """检查代码质量"""
    print("\n🔍 检查代码质量...")
    
    screen_manager_file = "main/screen_manager.c"
    if not os.path.exists(screen_manager_file):
        print(f"❌ 文件不存在: {screen_manager_file}")
        return False
    
    with open(screen_manager_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    quality_checks = [
        ("错误处理", "ESP_LOGE"),
        ("信息日志", "ESP_LOGI"),
        ("内存安全", "safe_delete_screen"),
        ("资源清理", "qrcode_canvas_safe_cleanup"),
        ("参数验证", "if (!"),
    ]
    
    all_passed = True
    for check_name, pattern in quality_checks:
        if pattern in content:
            print(f"✅ {check_name}: 代码中包含相关实现")
        else:
            print(f"⚠️  {check_name}: 可能需要检查")
    
    return True

def generate_test_report():
    """生成测试报告"""
    print("\n📊 生成测试报告...")
    
    # 执行所有检查
    button_fix = check_button_text_fix()
    qr_fix = check_qr_code_fix()
    scrollbar_fix = check_scrollbar_removal()
    code_quality = check_code_quality()
    
    # 生成报告
    report = f"""
# ESP32-S3 LVGL 屏幕修复验证报告

## 测试时间
{__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 测试结果

### 1. 按键文字修复
- 状态: {'✅ 通过' if button_fix else '❌ 失败'}
- 检查项: 按键文字从 "Left Button"/"Right Button" 改为 "Left"/"Right"

### 2. QR码重复显示修复
- 状态: {'✅ 通过' if qr_fix else '❌ 失败'}
- 检查项: QR码屏幕强制重建逻辑

### 3. 滚动条移除
- 状态: {'✅ 通过' if scrollbar_fix else '❌ 失败'}
- 检查项: 所有屏幕都禁用滚动条

### 4. 代码质量
- 状态: {'✅ 通过' if code_quality else '❌ 失败'}
- 检查项: 错误处理、日志记录、内存安全等

## 总体评估

{'✅ 所有修复验证通过，可以投入使用' if all([button_fix, qr_fix, scrollbar_fix, code_quality]) else '❌ 部分检查未通过，需要进一步修复'}

## 建议

1. 在实际硬件上测试QR码多次循环显示
2. 验证按键文字在240x320屏幕上的显示效果
3. 确认内存使用情况稳定
4. 测试触摸交互功能正常

---
验证脚本: test_screen_fixes.py
"""
    
    with open("SCREEN_FIXES_TEST_REPORT.md", "w", encoding="utf-8") as f:
        f.write(report)
    
    print("✅ 测试报告已生成: SCREEN_FIXES_TEST_REPORT.md")
    
    return all([button_fix, qr_fix, scrollbar_fix, code_quality])

def main():
    """主函数"""
    print("🚀 ESP32-S3 LVGL 屏幕修复验证")
    print("=" * 50)
    
    # 检查当前目录
    if not os.path.exists("main/screen_manager.c"):
        print("❌ 请在项目根目录运行此脚本")
        sys.exit(1)
    
    # 生成测试报告
    success = generate_test_report()
    
    # 总结
    print("\n" + "=" * 50)
    if success:
        print("🎉 所有修复验证通过!")
        print("   - 按键文字已正确修改")
        print("   - QR码重复显示问题已修复")
        print("   - 滚动条已完全移除")
        print("   - 代码质量良好")
        return 0
    else:
        print("⚠️  部分验证未通过，请检查修复情况")
        return 1

if __name__ == "__main__":
    sys.exit(main())
